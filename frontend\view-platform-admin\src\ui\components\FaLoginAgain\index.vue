<script setup lang="ts">
import LoginAgainForm from '@/components/AccountForm/LoginAgainForm.vue'
import eventBus from '@/utils/eventBus'

defineOptions({
  name: 'FaLoginAgain',
})

const route = useRoute()

const isShow = ref(false)

onMounted(() => {
  eventBus.on('global-login-again-visible', () => {
    if (route.name === 'login') {
      return
    }
    isShow.value = true
  })
})

watch(() => route.name, (val) => {
  if (val === 'login') {
    isShow.value = false
  }
}, {
  immediate: true,
})

async function handleAfterLogin() {
  isShow.value = false
}
</script>

<template>
  <FaModal v-model="isShow" :z-index="10000" :header="false" :footer="false" :closable="false" :close-on-click-overlay="false" :close-on-press-escape="false">
    <LoginAgainForm @on-after-login="handleAfterLogin" />
  </FaModal>
</template>
